////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file OversetWallDistanceCalculator.h
//! <AUTHOR>
//! @brief 重叠网格壁面距离计算器
//! @date 2024-12-19
//
//------------------------------修改日志----------------------------------------
// 2024-12-19 曾凯
//    说明：从OversetMesh类中分离壁面距离计算功能，提高代码模块化程度
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_OversetWallDistanceCalculator_
#define _specialModule_oversetMesh_OversetWallDistanceCalculator_

#include "meshProcess/zone/ZoneManager.h"
#include "meshProcess/wallDistance/WallDistanceManager.h"
#include "sourceFlow/configure/FlowConfigure.h"
#include "feilian-specialmodule/oversetMesh/OverDefines.h"
#include "feilian-external/linux-gcc7.3.0/boost/include/boost/mpi.hpp"

namespace Overset
{
    // 前向声明
    class OversetDonorSearcher;

    /**
     * @brief 重叠网格壁面距离计算器
     *
     * 负责计算网格单元到各子域壁面的距离，支持KDT树加速和并行计算
     */
    class OversetWallDistanceCalculator
    {
    public:
        /**
         * @brief 构造函数
         *
         * @param mesh_ 网格指针
         * @param zoneManager_ 域管理器指针
         * @param flowConfig_ 流场配置
         * @param mpi_world_ MPI通信器
         */
        OversetWallDistanceCalculator(Mesh *mesh_,
                                      ZoneManager *zoneManager_,
                                      const Configure::Flow::FlowConfigure &flowConfig_,
                                      const boost::mpi::communicator &mpi_world_);

        /**
         * @brief 析构函数
         */
        ~OversetWallDistanceCalculator();

        /**
         * @brief 初始化壁面距离计算器
         *
         * @param wallDistMethod 壁面距离计算方法
         * @param elemTypeMethod 单元类型判断方法
         */
        void Initialize(Turbulence::WallDistance wallDistMethod,
                        ElemTypeMethod elemTypeMethod);

        /**
         * @brief 计算所有单元到各子域壁面的距离
         *
         * @param donorSearcher 贡献单元搜索器指针（用于距离修正）
         */
        void CalculateWallDistances(OversetDonorSearcher *donorSearcher = nullptr);

        /**
         * @brief 更新网格中的壁面距离场
         */
        void UpdateWallDistField();

        /**
         * @brief 获取指定单元到指定子域的壁面距离
         *
         * @param elemID 单元编号
         * @param zoneID 子域编号
         * @return Scalar 壁面距离
         */
        Scalar GetWallDistance(int elemID, int zoneID) const;

        /**
         * @brief 判断指定单元的最短壁面距离是否来自自身子域
         *
         * @param elemID 单元编号
         * @param elemZoneID 单元所在子域编号
         * @return true 最短距离来自自身子域
         * @return false 最短距离来自其他子域
         */
        bool IsNearestWallDistToSelf(int elemID, int elemZoneID) const;

        /**
         * @brief 获取所有壁面距离数据（只读访问）
         *
         * @return const List<List<Scalar>>& 壁面距离数据
         */
        const List<List<Scalar>> &GetWallDistances() const { return wallDistances; }

        /**
         * @brief 获取最近壁面面元ID数据（只读访问）
         *
         * @return const List<List<int>>& 最近壁面面元ID数据
         */
        const List<List<int>> &GetNearestWallFaceIDs() const { return nearestWallFaceID; }

        /**
         * @brief 清理计算数据
         */
        void Clear();

    private:
        /**
         * @brief 收集全局壁面边界面元
         */
        void CollectGlobalWallFaces();

        /**
         * @brief 对计算出的壁面距离进行法向修正
         *
         * @param donorSearcher 贡献单元搜索器指针
         */
        void CorrectWallDist(OversetDonorSearcher *donorSearcher);

    private:
        // 基础数据
        Mesh *localMesh;                                  // 当前进程网格指针
        ZoneManager *zoneManager;                         // 域管理器指针
        const Configure::Flow::FlowConfigure &flowConfig; // 流场配置
        const boost::mpi::communicator &mpi_world;        // MPI通信器
        int n_Zones;                                      // 网格子域个数
        Mesh::MeshDim dim;                                // 网格维度
        int processorID;                                  // 当前进程ID
        int nProcessor;                                   // 总进程数

        // 计算方法配置
        Turbulence::WallDistance wallDistMethod; // 壁面距离计算方法
        ElemTypeMethod elemTypeMethod;           // 单元类型判断方法

        // 壁面距离管理器
        WallDistanceManager *wallDistanceManager; // 壁面距离计算管理器

        // 壁面数据（用于重叠网格特殊处理）
        List<std::vector<std::pair<Face, std::vector<Node>>>> globalWallFaceVectors; // 各子域的壁面面元向量

        // 计算结果
        List<List<Scalar>> wallDistances;  // 网格点或单元到各子域壁面的最短距离
        List<List<int>> nearestWallFaceID; // 最近壁面面元ID
    };
}

#endif
